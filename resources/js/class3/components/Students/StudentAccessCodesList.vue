<template>
    <div>
        <div class="d-flex justify-content-between align-items-center">
            <strong>{{ ucFirst(translateChoice('generic.students', 2)) }}</strong>
            <div class="d-flex align-items-center">
                <input
                    type="text"
                    class="form-control mx-3"
                    :placeholder="translate('generic.search')"
                    v-model="searchstudentsfilterkey"
                >
                <div class="mx-3">
                    {{ filteredStudents.length }}&nbsp;/&nbsp;{{ students.length }}
                </div>
            </div>
        </div>
        <table class="table mt-2">
            <thead>
            <tr>
                <th>{{ ucFirst(translate("generic.functions")) }}</th>
                <th>{{ ucFirst(translate("generic.hasaccess")) }}</th>
                <th>{{ ucFirst(translateChoice("generic.accesscodes", 1)) }}</th>
                <th>{{ ucFirst(translate("generic.name")) }}</th>
                <th>{{ ucFirst(translate("generic.lasttimeemailed")) }}</th>
                <th>{{ ucFirst(translate("generic.lasttimefilledin")) }}</th>
            </tr>
            </thead>
            <tbody>
            <tr
                v-for="(student, index) in filteredStudents"
                :key="index"
                :class="{'background-danger': !student.accesstoken}"
            >
                <td>
                    <template v-if="student.has_access">
                        <button
                            class="btn btn-danger btn-sm"
                            v-tooltip="translate('generic.revokeaccess')"
                            @click.prevent="removeAccess(student)"
                            :disabled="!student.accesstoken"
                        >
                            <font-awesome-icon icon="fa-solid fa-minus" />
                        </button>
                        <button
                            class="btn btn-success btn-sm"
                            v-tooltip="translate('generic.emailthisstudent')"
                            :data-bs-target="`#single-student-email-${student.studentId}`"
                            data-bs-toggle="modal"
                            :disabled="!student.accesstoken"
                        >
                            <font-awesome-icon icon="fa-solid fa-envelope" />
                        </button>
                        <EmailStudentWithAccess :modal-id="`single-student-email-${student.studentId}`" :student="student"/>
                    </template>
                    <button v-else class="btn btn-primary btn-sm"
                        @click.prevent="addAccess(student)"
                        v-tooltip="translate('generic.grantaccess')"
                        :disabled="!student.accesstoken"
                    >
                        <font-awesome-icon icon="fa-solid fa-plus" />
                    </button>
                    <a
                        :href="'/students/preferredschedule/' + student.studentId"
                        class="btn btn-default btn-sm"
                        v-tooltip="translate('generic.viewstudentspreferencepage')">
                        <font-awesome-icon icon="fa-solid fa-link" />
                    </a>
                </td>
                <td>
                    <font-awesome-icon
                        v-if="student.has_access"
                        icon="fa-solid fa-unlock"
                        class="text-success"
                    ></font-awesome-icon>
                    <font-awesome-icon
                        v-else
                        icon="fa-solid fa-lock"
                        class="text-danger"
                    ></font-awesome-icon>
                </td>
                <td v-if="student.accesstoken">{{ student.accesstoken }}</td>
                <td v-else class="text-danger">
                    <strong>{{ translate('generic.noaccestoken') }}!</strong>
                    <span v-tooltip="translate('generic.explainnoaccesstoken')">
                        <font-awesome-icon icon="question-circle" />
                    </span>
                </td>
                <td>
                    <!-- if there's nothing to join, student_id will be null -->
                    <a v-if="student.studentId != null" :href="'/students/' + student.studentId + '/edit'">{{ student.name }}</a>
                    <span v-else>{{ student.name }}</span>
                </td>
                <td>{{ displayDate(emailArchive[student.accesstoken]) }}</td>
                <td>{{ displayDate(student.lastSchedulePrefChanged) }}</td>
            </tr>
            </tbody>
        </table>
    </div>
</template>

<script setup>
import useStudentAccessCodes from "../../composables/useStudentAccessCodes.js";
import useLang from '../../composables/useLang.js';
import useDateTime from "../../composables/useDateTime.js";
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import EmailStudentWithAccess from "./EmailStudentWithAccess.vue";

const { ucFirst, translate, translateChoice } = useLang();
const { displayDate } = useDateTime();
const {
    addAccess,
    emailArchive,
    filteredStudents,
    removeAccess,
    searchstudentsfilterkey,
    students,
} = useStudentAccessCodes();

</script>

<style lang="scss" scoped>
@use "sass:color";
@use '../../../../sass/tmpl3/variables' as *;

.background-danger {
    background-color: #e08a8a; /* lighter version of class red */
}
</style>
