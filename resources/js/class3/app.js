/* *************************************************************
 * This file focuses solely on Vue application initialization
 * and configuration
 * ************************************************************* */
import { createApp } from 'vue';
import FloatingVue from 'floating-vue';
import 'floating-vue/dist/style.css';
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import { components } from './generated/component-imports.js';
import { initLogoutButton } from './globals.js';
import './globals.js';

// if we have signregistration in the URL: don't load Vue!
if (window.location.href.includes('signregistration')) {
    console.log('Skipping Vue - signregistration page');
    initLogoutButton();
} else {
    console.log('Starting Vue app initialization...');
    const app = createApp({
        mounted() {
            console.log('✅ CLASS Vue app mounted successfully!');
        }
    });

    // Register components
    console.log(`Registering ${Object.keys(components).length} Vue components...`);
    Object.entries(components).forEach(([name, component]) => {
        app.component(name, component);
    });

    // Use plugins
    console.log('Setting up Vue plugins...');
    app.use(FloatingVue, {
        themes: {
            'tooltip': {
                placement: 'top',
                delay: { show: 500, hide: 0 }
            }
        }
    });
    app.component('VueDatepicker', VueDatepicker);

    // Mount the app
    console.log('Mounting Vue app to #vuecontext...');
    const mountElement = document.querySelector('#vuecontext');
    if (mountElement) {
        app.mount('#vuecontext');
        console.log('✅ Vue app mounted successfully!');
    } else {
        console.error('❌ Could not find #vuecontext element to mount Vue app!');
    }

    // Initialize logout button after Vue is mounted
    console.log('Initializing logout button...');
    initLogoutButton();
}
