/* *************************************************************
 * This file focuses solely on Vue application initialization
 * and configuration
 * ************************************************************* */
import { createApp } from 'vue';
import FloatingVue from 'floating-vue';
import 'floating-vue/dist/style.css';
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import { components } from './generated/component-imports.js';
import { initLogoutButton } from './globals.js';
import './globals.js';

// Add global error handlers
window.addEventListener('error', (event) => {
    console.error('❌ Global JavaScript Error:', event.error);
    console.error('Error details:', event);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('❌ Unhandled Promise Rejection:', event.reason);
    console.error('Promise rejection details:', event);
});

// if we have signregistration in the URL: don't load Vue!
if (window.location.href.includes('signregistration')) {
    console.log('Skipping Vue - signregistration page');
    initLogoutButton();
} else {
    console.log('Starting Vue app initialization...');

    try {
        // Get the existing content before Vue mounts
        const contentElement = document.querySelector('#vue-content');
        const existingContent = contentElement ? contentElement.innerHTML : '';

        const app = createApp({
            template: `<div v-html="existingContent"></div>`,
            data() {
                return {
                    existingContent: existingContent
                };
            },
            mounted() {
                console.log('✅ CLASS Vue app mounted successfully!');
                console.log('Vue app instance:', this);
                console.log('Preserved content length:', this.existingContent.length);
            },
            errorCaptured(err, instance, info) {
                console.error('❌ Vue Error Captured:', err);
                console.error('Component instance:', instance);
                console.error('Error info:', info);
                return false; // Don't propagate to global handler
            }
        });

        // Add global error handler for Vue
        app.config.errorHandler = (err, instance, info) => {
            console.error('❌ Vue Global Error Handler:', err);
            console.error('Component instance:', instance);
            console.error('Error info:', info);
        };

        // Register components
        console.log(`Registering ${Object.keys(components).length} Vue components...`);
        Object.entries(components).forEach(([name, component]) => {
            try {
                app.component(name, component);
            } catch (err) {
                console.error(`❌ Error registering component ${name}:`, err);
            }
        });

        // Use plugins
        console.log('Setting up Vue plugins...');
        try {
            app.use(FloatingVue, {
                themes: {
                    'tooltip': {
                        placement: 'top',
                        delay: { show: 500, hide: 0 }
                    }
                }
            });
            app.component('VueDatepicker', VueDatepicker);
            console.log('✅ Plugins registered successfully');
        } catch (err) {
            console.error('❌ Error setting up plugins:', err);
        }

        // Mount the app
        console.log('Mounting Vue app to #vue-content...');
        const mountElement = document.querySelector('#vue-content');
        if (mountElement) {
            console.log('Found mount element:', mountElement);
            try {
                const mountedApp = app.mount('#vue-content');
                console.log('✅ Vue app mounted successfully!', mountedApp);

                // Keep a reference to check if it gets unmounted
                window.vueApp = mountedApp;

                // Monitor for changes to the content
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'childList') {
                            console.log('🔍 Content changed!', {
                                addedNodes: mutation.addedNodes.length,
                                removedNodes: mutation.removedNodes.length,
                                target: mutation.target,
                                newContent: mutation.target.innerHTML.substring(0, 100)
                            });
                        }
                    });
                });

                observer.observe(mountElement, {
                    childList: true,
                    subtree: true
                });

                setTimeout(() => {
                    console.log('Vue app status after 2 seconds:', window.vueApp);
                    console.log('Mount element content:', mountElement.innerHTML.substring(0, 200));
                    console.log('Vue component data:', window.vueApp.existingContent.substring(0, 200));
                }, 2000);

                setTimeout(() => {
                    console.log('Vue app status after 5 seconds:', window.vueApp);
                    console.log('Mount element content after 5s:', mountElement.innerHTML.substring(0, 200));
                }, 5000);

            } catch (err) {
                console.error('❌ Error mounting Vue app:', err);
            }
        } else {
            console.error('❌ Could not find #vue-content element to mount Vue app!');
        }

        // Initialize logout button after Vue is mounted
        console.log('Initializing logout button...');
        initLogoutButton();

    } catch (err) {
        console.error('❌ Error during Vue app initialization:', err);
    }
}
