.sb-nav-fixed {
  .sb-topnav {
    // Bootstrap .fixed-top equivalent
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: $zindex-topnav;
  }
  #layoutSidenav {
    #layoutSidenav_nav {
      // Bootstrap .fixed-top equivalent
      position: fixed;
      top: 0;
      right: 0;
      left: 0;
      width: $sidenav-base-width;
      height: 100vh;
      z-index: $zindex-sidenav;
      .sb-sidenav {
        padding-top: $topnav-base-height;
        .sb-sidenav-menu {
          overflow-y: auto;
        }
      }
    }
    #layoutSidenav_content {
      // padding-left: $sidenav-base-width; // content now gets full widt, no more left menu side bar
      top: $topnav-base-height;
    }
  }
}
