// Z index variables
$zindex-content: 1037 !default;
$zindex-sidenav: 1038 !default;
$zindex-topnav: 1039 !default;

// Basic colors are now defined in main variables.scss

// Color variables for the sidenav
// -- Sidenav Dark
$sidenav-dark-bg: $classblue;
$sidenav-dark-color: rgba(255, 255, 255, 0.7); // fade-out($white, 0.3)
$sidenav-dark-heading-color: rgba(255, 255, 255, 0.25); // fade-out($white, 0.75)
$sidenav-dark-link-color: rgba(255, 255, 255, 0.7); // fade-out($white, 0.3)
$sidenav-dark-link-active-color: $white;
$sidenav-dark-icon-color: rgba(255, 255, 255, 0.25); // fade-out($white, 0.75)
$sidenav-dark-footer-bg: $gray-800;

// -- Sidenav Light
$sidenav-light-bg: $gray-100;
$sidenav-light-color: $gray-900;
$sidenav-light-heading-color: $gray-500;
$sidenav-light-link-color: $sidenav-light-color;
$sidenav-light-link-active-color: $primary;
$sidenav-light-icon-color: $gray-500;
$sidenav-light-footer-bg: $gray-200;

// Color variables for the topnav
$topnav-dark-toggler-color: rgba(255, 255, 255, 0.5); // fade-out($white, 0.5)
$topnav-light-toggler-color: $gray-900;
