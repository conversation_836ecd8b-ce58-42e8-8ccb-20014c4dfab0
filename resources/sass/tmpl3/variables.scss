// CLASS Brand colors
$classred: #C75454;
$classblue: #6A6E8F ;
$classdarkblue: #425668;
$classgreen: #4C9B5E;
$classyellow: #EDA707;
$classbgblue: #6A6E8F;
$classlightblue: #66B6C3;
$classlight: #f8f9fa;
$classdark:#343a40;

$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-700: #495057;
$gray-800: #343a40;

// --------------------------------------------------------------
// Bootstrap 4 color overrides
// --------------------------------------------------------------
// These should go before bootstrap include in styles.scss
// Bootstrap will use it's own definition if it's not present
// here because of the !default flag for all styles in BS4

// BS override
$primary: $classblue;
$success: $classgreen;
$danger: $classred;
$warning: $classyellow;

// Site Theme
$theme-colors: (
    "primary": $primary,
    "success": $success,
    "danger": $danger,
    "warning": $warning
);

// Import all variable files
@import "variables/spacing";
@import "variables/navigation";
