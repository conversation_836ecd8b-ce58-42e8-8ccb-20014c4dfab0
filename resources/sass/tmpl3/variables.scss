// Define CSS custom properties for CLASS brand colors
:root {
  // CLASS Brand colors
  --class-red: #C75454;
  --class-blue: #6A6E8F;
  --class-dark-blue: #425668;
  --class-green: #4C9B5E;
  --class-yellow: #EDA707;
  --class-bg-blue: #6A6E8F;
  --class-light-blue: #66B6C3;
  --class-light: #f8f9fa;
  --class-dark: #343a40;

  // Navigation sizing
  --topnav-base-height: 56px;
  --sidenav-base-width: 225px;
}

// Keep SCSS variables for backward compatibility during transition
$classred: var(--class-red);
$classblue: var(--class-blue);
$classdarkblue: var(--class-dark-blue);
$classgreen: var(--class-green);
$classyellow: var(--class-yellow);
$classbgblue: var(--class-bg-blue);
$classlightblue: var(--class-light-blue);
$classlight: var(--class-light);
$classdark: var(--class-dark);

// Navigation variables
$topnav-base-height: var(--topnav-base-height);
$sidenav-base-width: var(--sidenav-base-width);

// Bootstrap navbar variables (Bootstrap 5 defaults)
$navbar-padding-x: 1rem;

// Bootstrap color overrides using CSS custom properties
$primary: var(--class-blue);
$success: var(--class-green);
$danger: var(--class-red);
$warning: var(--class-yellow);

// Gray colors and basic colors (keeping as SCSS for now)
$white: #ffffff;
$black: #000000;
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;

// Import navigation variables
@import "variables/navigation";
