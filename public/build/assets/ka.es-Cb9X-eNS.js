/**
  * vue-cal v4.10.2
  * (c) 2025 <PERSON><PERSON> <<EMAIL>>
  * @license MIT
  */const t=["ორშაბათი","სამშაბათი","ოთხშაბათი","ხუთშაბათი","პარასკევი","შაბათი","კვირა"],n=["იანვარი","თებერვალი","მარტი","აპრილი","მაისი","ივნისი","ივლისი","აგვისტო","სექტემბერი","ოქტომბერი","ნოემბერი","დეკემბერი"],o="წლები",s="წელი",e="თვე",c="კვირა",a="დღე",d="დღეს",y="ღონისძიება არ არის",r="მთელი დღე",l="წაშლა",M="შექმენით ღონისძიება",Y="dddd D MMMM YYYY",k={weekDays:t,months:n,years:o,year:s,month:e,week:c,day:a,today:d,noEvent:y,allDay:r,deleteEvent:l,createEvent:M,dateFormat:Y};export{r as allDay,M as createEvent,Y as dateFormat,a as day,k as default,l as deleteEvent,e as month,n as months,y as noEvent,d as today,c as week,t as weekDays,s as year,o as years};
