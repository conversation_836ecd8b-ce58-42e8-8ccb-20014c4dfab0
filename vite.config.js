import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/sass/app.scss',
                'resources/js/class3/app.js'
            ],
            refresh: true,
            detectTls: false,
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './resources/js/class3'),
            '~': path.resolve(__dirname, './node_modules'),
        },
    },
    css: {
        preprocessorOptions: {
            scss: {
                api: 'modern',
                includePaths: ['node_modules']
            }
        }
    },
    server: {
        hmr: false, // Disable HMR for testing
    },
    build: {
        outDir: 'public/build',
        manifest: true,
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['vue', 'axios'],
                    ui: ['@vueform/multiselect', '@vuepic/vue-datepicker', 'floating-vue'],
                    calendar: ['@fullcalendar/core', '@fullcalendar/vue3', '@fullcalendar/daygrid', '@fullcalendar/timegrid'],
                }
            }
        }
    }
});
